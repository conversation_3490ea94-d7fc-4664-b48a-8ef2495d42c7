#!/usr/bin/env node

/**
 * 🌐 Gerador de Página HTML Interativa
 * 
 * Este script gera uma página HTML interativa com o mapeamento completo
 * de ações e permissões, incluindo filtros e busca.
 * 
 * Uso: node gerar_html.cjs
 */

const fs = require('fs');
const path = require('path');

// Configurações
const DADOS_FILE = path.join(__dirname, 'dados_funcionalidades.json');
const OUTPUT_FILE = path.join(__dirname, 'Mapeamento_Acoes_Funcionalidades_com_Constantes.html');

console.log('🌐 Iniciando geração da página HTML...\n');

// Função para gerar HTML
function gerarHTML() {
  // Verificar se os dados existem
  if (!fs.existsSync(DADOS_FILE)) {
    console.error('❌ Arquivo de dados não encontrado! Execute primeiro: node analisar_funcionalidades.cjs');
    return false;
  }
  
  // Carregar dados
  const dados = JSON.parse(fs.readFileSync(DADOS_FILE, 'utf8'));
  console.log(`📄 Carregados dados de ${dados.acoes.length} ações`);
  
  // Gerar HTML
  const html = `<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Mapeamento de Permissões - Sistema Multiempresa</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
        
        .controls {
            padding: 30px;
            background: white;
            border-bottom: 1px solid #eee;
        }
        
        .search-box {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .filter-btn {
            padding: 10px 20px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn:hover {
            background: #f8f9fa;
        }
        
        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .table-container {
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            background: #2c3e50;
            color: white;
            padding: 15px 10px;
            text-align: left;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        td {
            padding: 12px 10px;
            border-bottom: 1px solid #eee;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .permission-yes {
            background: #27ae60;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .permission-no {
            background: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .action-name {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .footer {
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            color: #7f8c8d;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.8em;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filters {
                justify-content: center;
            }
            
            th, td {
                padding: 8px 5px;
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Mapeamento de Permissões</h1>
            <p>Sistema Multiempresa - Gerado em ${new Date().toLocaleString('pt-BR')}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${dados.estatisticas.totalAcoes}</div>
                <div class="stat-label">Total de Ações</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${dados.estatisticas.totalArquivos}</div>
                <div class="stat-label">Arquivos Analisados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${dados.tiposUsuario.length}</div>
                <div class="stat-label">Tipos de Usuário</div>
            </div>
        </div>
        
        <div class="controls">
            <input type="text" class="search-box" id="searchBox" placeholder="🔍 Buscar ações...">
            
            <div class="filters">
                <button class="filter-btn active" onclick="filtrarTodos()">Todos</button>
                ${dados.tiposUsuario.map(tipo => 
                    `<button class="filter-btn" onclick="filtrarPorTipo('${tipo}')">${tipo.replace('_', ' ')}</button>`
                ).join('')}
            </div>
        </div>
        
        <div class="table-container">
            <table id="permissionsTable">
                <thead>
                    <tr>
                        <th>Ação</th>
                        ${dados.tiposUsuario.map(tipo => `<th>${tipo.replace('_', ' ')}</th>`).join('')}
                    </tr>
                </thead>
                <tbody id="tableBody">
                    ${dados.acoes.map(acao => `
                        <tr data-action="${acao.nomeCompleto.toLowerCase()}">
                            <td class="action-name">${acao.nomeCompleto}</td>
                            ${dados.tiposUsuario.map(tipo => 
                                `<td><span class="permission-${acao.permissoes[tipo] ? 'yes' : 'no'}">${acao.permissoes[tipo] ? 'SIM' : 'NÃO'}</span></td>`
                            ).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>📊 Dados extraídos automaticamente dos arquivos TypeScript da pasta Funcionalidades</p>
            <p>🔄 Última atualização: ${dados.timestamp}</p>
        </div>
    </div>
    
    <script>
        // Dados para JavaScript
        const dadosPermissoes = ${JSON.stringify(dados)};
        
        // Função de busca
        document.getElementById('searchBox').addEventListener('input', function(e) {
            const termo = e.target.value.toLowerCase();
            const linhas = document.querySelectorAll('#tableBody tr');
            
            linhas.forEach(linha => {
                const acao = linha.dataset.action;
                if (acao.includes(termo)) {
                    linha.style.display = '';
                } else {
                    linha.style.display = 'none';
                }
            });
        });
        
        // Função para filtrar todos
        function filtrarTodos() {
            const linhas = document.querySelectorAll('#tableBody tr');
            linhas.forEach(linha => linha.style.display = '');
            
            // Atualizar botões
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }
        
        // Função para filtrar por tipo de usuário
        function filtrarPorTipo(tipo) {
            const linhas = document.querySelectorAll('#tableBody tr');
            const indiceColuna = dadosPermissoes.tiposUsuario.indexOf(tipo) + 1; // +1 porque a primeira coluna é a ação
            
            linhas.forEach(linha => {
                const celula = linha.children[indiceColuna];
                const temPermissao = celula.textContent.trim() === 'SIM';
                
                if (temPermissao) {
                    linha.style.display = '';
                } else {
                    linha.style.display = 'none';
                }
            });
            
            // Atualizar botões
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }
    </script>
</body>
</html>`;
  
  // Salvar arquivo
  fs.writeFileSync(OUTPUT_FILE, html, 'utf8');
  
  console.log(`✅ Página HTML gerada com sucesso!`);
  console.log(`📁 Arquivo: ${OUTPUT_FILE}`);
  console.log(`📊 ${dados.acoes.length} ações mapeadas`);
  console.log(`👥 ${dados.tiposUsuario.length} tipos de usuário`);
  console.log(`🌐 Página interativa com busca e filtros`);
  
  return true;
}

// Executar geração se chamado diretamente
if (require.main === module) {
  gerarHTML();
}

module.exports = { gerarHTML };
