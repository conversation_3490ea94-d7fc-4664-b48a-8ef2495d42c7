#!/usr/bin/env node

/**
 * 📊 Gerador de Planilha Excel
 * 
 * Este script gera uma planilha Excel com o mapeamento completo
 * de ações e permissões baseado nos dados analisados.
 * 
 * Uso: node gerar_excel.cjs
 */

const fs = require('fs');
const path = require('path');
const XLSX = require('xlsx');

// Configurações
const DADOS_FILE = path.join(__dirname, 'dados_funcionalidades.json');
const OUTPUT_FILE = path.join(__dirname, 'Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx');

console.log('📊 Iniciando geração da planilha Excel...\n');

// Função para gerar planilha Excel
function gerarExcel() {
  // Verificar se os dados existem
  if (!fs.existsSync(DADOS_FILE)) {
    console.error('❌ Arquivo de dados não encontrado! Execute primeiro: node analisar_funcionalidades.cjs');
    return false;
  }
  
  // Carregar dados
  const dados = JSON.parse(fs.readFileSync(DADOS_FILE, 'utf8'));
  console.log(`📄 Carregados dados de ${dados.acoes.length} ações`);
  
  // Preparar dados para a planilha
  const linhas = [];
  
  // Cabeçalho
  const cabecalho = ['Ação', ...dados.tiposUsuario];
  linhas.push(cabecalho);
  
  // Dados das ações
  dados.acoes.forEach(acao => {
    const linha = [acao.nomeCompleto];
    
    dados.tiposUsuario.forEach(tipo => {
      linha.push(acao.permissoes[tipo] ? 'SIM' : 'NÃO');
    });
    
    linhas.push(linha);
  });
  
  // Criar workbook
  const workbook = XLSX.utils.book_new();
  
  // Criar worksheet principal
  const worksheet = XLSX.utils.aoa_to_sheet(linhas);
  
  // Configurar largura das colunas
  const colunaLarguras = [
    { wch: 50 }, // Coluna Ação (mais larga)
    ...dados.tiposUsuario.map(() => ({ wch: 15 })) // Colunas de permissões
  ];
  worksheet['!cols'] = colunaLarguras;
  
  // Adicionar worksheet ao workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Mapeamento de Permissões');
  
  // Criar worksheet de estatísticas
  const estatisticasLinhas = [
    ['📊 ESTATÍSTICAS DE PERMISSÕES'],
    [''],
    ['Métrica', 'Valor'],
    ['Total de Arquivos', dados.estatisticas.totalArquivos],
    ['Total de Ações', dados.estatisticas.totalAcoes],
    ['Data de Geração', new Date().toLocaleString('pt-BR')],
    [''],
    ['👥 PERMISSÕES POR TIPO DE USUÁRIO'],
    [''],
    ['Tipo de Usuário', 'Quantidade de Permissões']
  ];
  
  dados.tiposUsuario.forEach(tipo => {
    estatisticasLinhas.push([tipo, dados.estatisticas.permissoesPorTipo[tipo]]);
  });
  
  estatisticasLinhas.push(['']);
  estatisticasLinhas.push(['📁 AÇÕES POR ARQUIVO']);
  estatisticasLinhas.push(['']);
  estatisticasLinhas.push(['Arquivo', 'Quantidade de Ações']);
  
  Object.entries(dados.estatisticas.acoesPorArquivo).forEach(([arquivo, quantidade]) => {
    estatisticasLinhas.push([arquivo, quantidade]);
  });
  
  const estatisticasWorksheet = XLSX.utils.aoa_to_sheet(estatisticasLinhas);
  estatisticasWorksheet['!cols'] = [{ wch: 30 }, { wch: 20 }];
  
  XLSX.utils.book_append_sheet(workbook, estatisticasWorksheet, 'Estatísticas');
  
  // Salvar arquivo
  XLSX.writeFile(workbook, OUTPUT_FILE);
  
  console.log(`✅ Planilha Excel gerada com sucesso!`);
  console.log(`📁 Arquivo: ${OUTPUT_FILE}`);
  console.log(`📊 ${dados.acoes.length} ações mapeadas`);
  console.log(`👥 ${dados.tiposUsuario.length} tipos de usuário`);
  
  return true;
}

// Executar geração se chamado diretamente
if (require.main === module) {
  gerarExcel();
}

module.exports = { gerarExcel };
