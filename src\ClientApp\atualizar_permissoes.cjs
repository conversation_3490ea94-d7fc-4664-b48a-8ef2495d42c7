#!/usr/bin/env node

/**
 * 🔄 Script Principal de Atualização de Permissões
 *
 * Este script coordena todo o processo de atualização dos mapeamentos de permissões:
 * 1. Analisa todos os arquivos TypeScript da pasta Funcionalidades
 * 2. Extrai ações e permissões automaticamente
 * 3. Gera arquivo Excel atualizado
 * 4. Gera arquivo CSV atualizado
 * 5. Gera página HTML interativa atualizada
 * 6. Exibe estatísticas detalhadas
 *
 * Uso: node atualizar_permissoes.cjs
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 Iniciando atualização completa de permissões...\n');

// Função para executar scripts de forma sequencial
async function executarScript(nomeScript, descricao) {
  console.log(`📊 ${descricao}...`);

  try {
    // Importar e executar o script
    const scriptPath = path.join(__dirname, nomeScript);

    if (!fs.existsSync(scriptPath)) {
      console.log(`⚠️  Script ${nomeScript} não encontrado.`);
      return false;
    }

    // Executar o script usando spawn para capturar output
    const { spawn } = require('child_process');

    return new Promise((resolve) => {
      const child = spawn('node', [scriptPath], {
        stdio: 'inherit',
        cwd: __dirname
      });

      child.on('close', (code) => {
        if (code === 0) {
          console.log(`✅ ${descricao} concluído com sucesso\n`);
          resolve(true);
        } else {
          console.error(`❌ ${descricao} falhou com código ${code}\n`);
          resolve(false);
        }
      });

      child.on('error', (error) => {
        console.error(`❌ Erro ao executar ${descricao}:`, error.message);
        resolve(false);
      });
    });
  } catch (error) {
    console.error(`❌ Erro ao executar ${descricao}:`, error.message);
    return false;
  }
}

// Função principal
async function main() {
  const scripts = [
    { nome: 'analisar_funcionalidades.cjs', descricao: 'Analisando arquivos de Funcionalidades' },
    { nome: 'gerar_excel.cjs', descricao: 'Gerando planilha Excel' },
    { nome: 'gerar_csv_com_constantes.cjs', descricao: 'Gerando arquivo CSV' },
    { nome: 'gerar_html.cjs', descricao: 'Gerando página HTML interativa' }
  ];

  let sucessos = 0;

  for (const script of scripts) {
    const sucesso = await executarScript(script.nome, script.descricao);
    if (sucesso) sucessos++;
  }

  // Resumo final
  console.log('📋 RESUMO DA EXECUÇÃO:');
  console.log(`✅ Scripts executados com sucesso: ${sucessos}/${scripts.length}`);

  if (sucessos === scripts.length) {
    console.log('\n🎉 Atualização de permissões concluída com sucesso!');
    console.log('\n📁 Arquivos gerados:');
    console.log('  📊 Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx');
    console.log('  📄 Mapeamento_Acoes_Funcionalidades_com_Constantes.csv');
    console.log('  🌐 Mapeamento_Acoes_Funcionalidades_com_Constantes.html');
  } else {
    console.log('\n⚠️  Alguns scripts falharam. Verifique os logs acima.');
  }
}

// Executar script principal
main().catch(error => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
});
