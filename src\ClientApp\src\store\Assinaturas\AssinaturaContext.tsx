import React, { createContext, useState, useContext, useCallback } from 'react';

import { enumTipoServico } from 'constants/Enum/enumTipoServicos';

import { ServicosAdicionais } from 'pages/Assinaturas/Formulario/validationForms';
import { FormData } from 'pages/Assinaturas/Listar/validationForms';
import { DominioProps } from 'pages/Assinaturas/types';

interface AssinaturaProps {
  contaCliente: string;
  setContaCliente: React.Dispatch<React.SetStateAction<string>>;
  setListDataResponsavel: React.Dispatch<
    React.SetStateAction<ResponsavelProps>
  >;
  listDataResponsavel: ResponsavelProps;
  isCriarNovaLoja: boolean;
  setIsUpdateFormDominio: React.Dispatch<React.SetStateAction<boolean>>;
  isUpdateFormDominio: boolean;
  listServicosAdicionais: ServicosAdicionais[];
  setListServicosAdicionais: React.Dispatch<
    React.SetStateAction<ServicosAdicionais[]>
  >;
  listServicosContratados: ServicosAdicionais[];
  setListServicosContratados: React.Dispatch<
    React.SetStateAction<ServicosAdicionais[]>
  >;
  isPlanoAdicionado: boolean;
  currentValuesFiltrosAssinatura: FormData | undefined;
  setCurrentValuesFiltrosAssinatura: React.Dispatch<
    React.SetStateAction<FormData | undefined>
  >;
  dadosCancelamento: DadosCancelamentoProps | undefined;
  setDadosCancelamento: React.Dispatch<
    React.SetStateAction<DadosCancelamentoProps | undefined>
  >;
  setDataExpiracao: React.Dispatch<React.SetStateAction<Date | null>>;
  dataExpiracao: Date | null;
  existeDominioDelivery: boolean;
  setExisteDominioDelivery: React.Dispatch<React.SetStateAction<boolean>>;
}

export type ResponsavelProps = {
  nome: string;
  email: string;
  celular: string;
  dominio: string;
  dominios: DominioProps[];
};

type DadosCancelamentoProps = {
  dataCancelamento: Date | null;
  motivoCancelamento: string | null;
};

export const AssinaturaContext = createContext<AssinaturaProps>(
  {} as AssinaturaProps
);

interface AssinaturaProviderProps {
  children: React.ReactNode;
}

export default function AssinaturaProvider({
  children,
}: AssinaturaProviderProps): JSX.Element {
  const [contaCliente, setContaCliente] = useState('');
  const [isUpdateFormDominio, setIsUpdateFormDominio] = useState(false);
  const [listServicosAdicionais, setListServicosAdicionais] = useState<
    ServicosAdicionais[]
  >([]);
  const [listServicosContratados, setListServicosContratados] = useState<
    ServicosAdicionais[]
  >([]);
  const [listDataResponsavel, setListDataResponsavel] =
    useState<ResponsavelProps>({} as ResponsavelProps);
  const [currentValuesFiltrosAssinatura, setCurrentValuesFiltrosAssinatura] =
    useState<FormData>();
  const [dadosCancelamento, setDadosCancelamento] =
    useState<DadosCancelamentoProps>();
  const [existeDominioDelivery, setExisteDominioDelivery] = useState(false);
  const [dataExpiracao, setDataExpiracao] = useState<Date | null>(null);

  const isPlanoAdicionado = listServicosAdicionais.some(
    (servicoItem) => servicoItem.tipo === enumTipoServico.PLANO
  );

  const isCriarNovaLoja = useCallback(() => {
    if (listDataResponsavel?.nome) {
      return true;
    }
    return false;
  }, [listDataResponsavel])();

  return (
    <AssinaturaContext.Provider
      value={{
        contaCliente,
        setContaCliente,
        setListDataResponsavel,
        isCriarNovaLoja,
        listDataResponsavel,
        setIsUpdateFormDominio,
        isUpdateFormDominio,
        listServicosAdicionais,
        setListServicosAdicionais,
        listServicosContratados,
        setListServicosContratados,
        isPlanoAdicionado,
        currentValuesFiltrosAssinatura,
        setCurrentValuesFiltrosAssinatura,
        dadosCancelamento,
        setDadosCancelamento,
        existeDominioDelivery,
        setExisteDominioDelivery,
        setDataExpiracao,
        dataExpiracao,
      }}
    >
      {children}
    </AssinaturaContext.Provider>
  );
}

export function useAssinaturasContext(): AssinaturaProps {
  const context = useContext(AssinaturaContext);

  if (!context)
    throw new Error(
      'AssinaturasContext must be used within a AssinaturaProvider.'
    );

  return context;
}
