#!/usr/bin/env node

/**
 * 📊 Analisador de Arquivos de Funcionalidades
 * 
 * Este script analisa todos os arquivos TypeScript da pasta Funcionalidades
 * e extrai as ações e permissões definidas em cada arquivo.
 * 
 * Uso: node analisar_funcionalidades.cjs
 */

const fs = require('fs');
const path = require('path');

// Configurações
const FUNCIONALIDADES_PATH = path.join(__dirname, 'src', 'constants', 'Funcionalidades');
const ENUM_USUARIO_PATH = path.join(__dirname, 'src', 'constants', 'Enum', 'enumTipoUsuario.ts');
const OUTPUT_FILE = path.join(__dirname, 'dados_funcionalidades.json');

// Tipos de usuário baseados no enum
const TIPOS_USUARIO = [
  'SISTEMA_ADMIN',
  'SISTEMA_FINANCEIRO', 
  'DESENVOLVEDOR',
  'REVENDA_ADMIN',
  'COMERCIAL_STI3',
  'CS_STI3',
  'SUPORTE_STI3',
  'CANAIS_GERENTE',
  'ANALISTA_CONTEUDO'
];

console.log('📊 Iniciando análise dos arquivos de Funcionalidades...\n');

// Função para extrair ações de um arquivo TypeScript
function extrairAcoesDoArquivo(caminhoArquivo, nomeArquivo) {
  try {
    const conteudo = fs.readFileSync(caminhoArquivo, 'utf8');
    const acoes = [];
    
    // Extrair nome da constante exportada (ex: AssinaturaAcao)
    const matchConstante = conteudo.match(/export\s+const\s+(\w+)\s*=/);
    const nomeConstante = matchConstante ? matchConstante[1] : 'UnknownAcao';
    
    // Regex para encontrar ações (propriedades do objeto)
    const regexAcao = /(\w+):\s*\[([\s\S]*?)\]/g;
    let match;
    
    while ((match = regexAcao.exec(conteudo)) !== null) {
      const nomeAcao = match[1];
      const permissoesTexto = match[2];
      
      // Extrair tipos de usuário das permissões
      const permissoes = {};
      TIPOS_USUARIO.forEach(tipo => {
        permissoes[tipo] = permissoesTexto.includes(`EnumTipoUsuario.${tipo}`);
      });
      
      acoes.push({
        nome: nomeAcao,
        nomeCompleto: `${nomeConstante}.${nomeAcao}`,
        permissoes: permissoes,
        arquivo: nomeArquivo,
        constante: nomeConstante
      });
    }
    
    return acoes;
  } catch (error) {
    console.error(`❌ Erro ao processar arquivo ${nomeArquivo}:`, error.message);
    return [];
  }
}

// Função principal de análise
function analisarFuncionalidades() {
  console.log(`📁 Analisando pasta: ${FUNCIONALIDADES_PATH}`);
  
  if (!fs.existsSync(FUNCIONALIDADES_PATH)) {
    console.error('❌ Pasta Funcionalidades não encontrada!');
    return null;
  }
  
  const arquivos = fs.readdirSync(FUNCIONALIDADES_PATH)
    .filter(arquivo => arquivo.endsWith('.ts'));
  
  console.log(`📄 Encontrados ${arquivos.length} arquivos TypeScript\n`);
  
  const todasAcoes = [];
  const estatisticas = {
    totalArquivos: arquivos.length,
    totalAcoes: 0,
    acoesPorArquivo: {},
    permissoesPorTipo: {}
  };
  
  // Inicializar contadores de permissões
  TIPOS_USUARIO.forEach(tipo => {
    estatisticas.permissoesPorTipo[tipo] = 0;
  });
  
  // Processar cada arquivo
  arquivos.forEach(arquivo => {
    console.log(`🔍 Processando: ${arquivo}`);
    const caminhoCompleto = path.join(FUNCIONALIDADES_PATH, arquivo);
    const acoes = extrairAcoesDoArquivo(caminhoCompleto, arquivo);
    
    console.log(`  ✅ Encontradas ${acoes.length} ações`);
    
    todasAcoes.push(...acoes);
    estatisticas.acoesPorArquivo[arquivo] = acoes.length;
    estatisticas.totalAcoes += acoes.length;
    
    // Contar permissões por tipo
    acoes.forEach(acao => {
      TIPOS_USUARIO.forEach(tipo => {
        if (acao.permissoes[tipo]) {
          estatisticas.permissoesPorTipo[tipo]++;
        }
      });
    });
  });
  
  // Ordenar ações alfabeticamente
  todasAcoes.sort((a, b) => a.nomeCompleto.localeCompare(b.nomeCompleto));
  
  const resultado = {
    timestamp: new Date().toISOString(),
    estatisticas: estatisticas,
    tiposUsuario: TIPOS_USUARIO,
    acoes: todasAcoes
  };
  
  // Salvar resultado em arquivo JSON
  fs.writeFileSync(OUTPUT_FILE, JSON.stringify(resultado, null, 2));
  
  console.log('\n📊 ESTATÍSTICAS:');
  console.log(`📁 Arquivos processados: ${estatisticas.totalArquivos}`);
  console.log(`🎯 Total de ações: ${estatisticas.totalAcoes}`);
  console.log('\n👥 Permissões por tipo de usuário:');
  
  TIPOS_USUARIO.forEach(tipo => {
    console.log(`  ${tipo}: ${estatisticas.permissoesPorTipo[tipo]} permissões`);
  });
  
  console.log(`\n💾 Dados salvos em: ${OUTPUT_FILE}`);
  
  return resultado;
}

// Executar análise se chamado diretamente
if (require.main === module) {
  analisarFuncionalidades();
}

module.exports = { analisarFuncionalidades, TIPOS_USUARIO };
