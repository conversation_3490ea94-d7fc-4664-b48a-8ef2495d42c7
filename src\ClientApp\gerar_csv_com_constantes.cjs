#!/usr/bin/env node

/**
 * 📄 Gerador de Arquivo CSV
 * 
 * Este script gera um arquivo CSV com o mapeamento completo
 * de ações e permissões baseado nos dados analisados.
 * 
 * Uso: node gerar_csv_com_constantes.cjs
 */

const fs = require('fs');
const path = require('path');

// Configurações
const DADOS_FILE = path.join(__dirname, 'dados_funcionalidades.json');
const OUTPUT_FILE = path.join(__dirname, 'Mapeamento_Acoes_Funcionalidades_com_Constantes.csv');

console.log('📄 Iniciando geração do arquivo CSV...\n');

// Função para escapar valores CSV
function escaparCSV(valor) {
  if (typeof valor !== 'string') {
    valor = String(valor);
  }
  
  // Se contém vírgula, aspas ou quebra de linha, precisa ser escapado
  if (valor.includes(',') || valor.includes('"') || valor.includes('\n')) {
    // Duplicar aspas internas e envolver em aspas
    return '"' + valor.replace(/"/g, '""') + '"';
  }
  
  return valor;
}

// Função para gerar CSV
function gerarCSV() {
  // Verificar se os dados existem
  if (!fs.existsSync(DADOS_FILE)) {
    console.error('❌ Arquivo de dados não encontrado! Execute primeiro: node analisar_funcionalidades.cjs');
    return false;
  }
  
  // Carregar dados
  const dados = JSON.parse(fs.readFileSync(DADOS_FILE, 'utf8'));
  console.log(`📄 Carregados dados de ${dados.acoes.length} ações`);
  
  // Preparar linhas CSV
  const linhas = [];
  
  // Cabeçalho
  const cabecalho = ['Ação', ...dados.tiposUsuario];
  linhas.push(cabecalho.map(escaparCSV).join(','));
  
  // Dados das ações
  dados.acoes.forEach(acao => {
    const linha = [acao.nomeCompleto];
    
    dados.tiposUsuario.forEach(tipo => {
      linha.push(acao.permissoes[tipo] ? 'SIM' : 'NÃO');
    });
    
    linhas.push(linha.map(escaparCSV).join(','));
  });
  
  // Juntar todas as linhas
  const conteudoCSV = linhas.join('\n');
  
  // Salvar arquivo
  fs.writeFileSync(OUTPUT_FILE, conteudoCSV, 'utf8');
  
  console.log(`✅ Arquivo CSV gerado com sucesso!`);
  console.log(`📁 Arquivo: ${OUTPUT_FILE}`);
  console.log(`📊 ${dados.acoes.length} ações mapeadas`);
  console.log(`👥 ${dados.tiposUsuario.length} tipos de usuário`);
  console.log(`📄 ${linhas.length} linhas (incluindo cabeçalho)`);
  
  // Gerar arquivo de estatísticas separado
  const estatisticasCSV = gerarEstatisticasCSV(dados);
  const estatisticasFile = path.join(__dirname, 'Estatisticas_Permissoes.csv');
  fs.writeFileSync(estatisticasFile, estatisticasCSV, 'utf8');
  
  console.log(`📊 Arquivo de estatísticas: ${estatisticasFile}`);
  
  return true;
}

// Função para gerar CSV de estatísticas
function gerarEstatisticasCSV(dados) {
  const linhas = [];
  
  // Seção de estatísticas gerais
  linhas.push('Métrica,Valor');
  linhas.push(`Total de Arquivos,${dados.estatisticas.totalArquivos}`);
  linhas.push(`Total de Ações,${dados.estatisticas.totalAcoes}`);
  linhas.push(`Data de Geração,"${new Date().toLocaleString('pt-BR')}"`);
  linhas.push('');
  
  // Seção de permissões por tipo
  linhas.push('Tipo de Usuário,Quantidade de Permissões');
  dados.tiposUsuario.forEach(tipo => {
    linhas.push(`${tipo},${dados.estatisticas.permissoesPorTipo[tipo]}`);
  });
  linhas.push('');
  
  // Seção de ações por arquivo
  linhas.push('Arquivo,Quantidade de Ações');
  Object.entries(dados.estatisticas.acoesPorArquivo).forEach(([arquivo, quantidade]) => {
    linhas.push(`${escaparCSV(arquivo)},${quantidade}`);
  });
  
  return linhas.join('\n');
}

// Executar geração se chamado diretamente
if (require.main === module) {
  gerarCSV();
}

module.exports = { gerarCSV };
